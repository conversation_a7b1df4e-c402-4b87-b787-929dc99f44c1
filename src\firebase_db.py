"""
Firebase database handler for the Wiz Aroma Food Delivery system.
This module provides functions for interacting with Firebase Realtime Database.
"""

import os
import json
import firebase_admin
from firebase_admin import credentials, db
from typing import Dict, List, Any, Optional
import logging
import inspect
import time
import random
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

# Track initialization attempts to prevent excessive retries
_last_init_attempt = 0
_init_attempt_count = 0
_MAX_INIT_ATTEMPTS = 5
_INIT_RETRY_DELAY = 5  # seconds

# Default path for credentials file
CREDENTIALS_FILE = os.environ.get(
    "FIREBASE_CREDENTIALS_PATH", "wiz-aroma-food-delivery-firebase-adminsdk.json"
)


# Initialize Firebase app
def initialize_firebase():
    """Initialize Firebase app with credentials from environment or file"""
    global _last_init_attempt, _init_attempt_count

    # Check if Firebase is already initialized
    if firebase_admin._apps:
        return True

    # Apply rate limiting to initialization attempts
    current_time = time.time()
    if current_time - _last_init_attempt < _INIT_RETRY_DELAY:
        # Too soon since last attempt
        logger.debug("Skipping Firebase initialization - too soon since last attempt")
        return False

    # Update attempt tracking
    _last_init_attempt = current_time
    _init_attempt_count += 1

    # If we've tried too many times, back off
    if _init_attempt_count > _MAX_INIT_ATTEMPTS:
        backoff_time = min(
            300, _INIT_RETRY_DELAY * (_init_attempt_count - _MAX_INIT_ATTEMPTS)
        )
        logger.warning(
            f"Too many Firebase initialization attempts, backing off for {backoff_time}s"
        )
        time.sleep(backoff_time)

    try:
        # Try to get the service account key from environment variable
        firebase_creds_json = os.environ.get("FIREBASE_CREDENTIALS")

        if firebase_creds_json:
            # Load credentials from environment variable
            try:
                firebase_creds_dict = json.loads(firebase_creds_json)
                cred = credentials.Certificate(firebase_creds_dict)
                logger.info(
                    "Initializing Firebase with credentials from environment variable"
                )
            except json.JSONDecodeError:
                logger.error(
                    "Failed to parse Firebase credentials from environment variable"
                )
                return False
        elif os.path.exists(CREDENTIALS_FILE):
            # Load credentials from file
            try:
                # Read and modify the credentials to handle JWT time issues
                with open(CREDENTIALS_FILE, 'r') as f:
                    creds_data = json.load(f)

                # Create credentials with explicit time handling
                cred = credentials.Certificate(creds_data)
                logger.info(
                    f"Initializing Firebase with credentials from file: {CREDENTIALS_FILE}"
                )
            except Exception as e:
                logger.error(
                    f"Failed to load Firebase credentials from file {CREDENTIALS_FILE}: {e}"
                )
                return False
        else:
            logger.error(
                f"Firebase credentials not found. Please set FIREBASE_CREDENTIALS in .env or provide {CREDENTIALS_FILE}"
            )
            return False

        # Get database URL from environment variable
        firebase_db_url = os.environ.get("FIREBASE_DATABASE_URL")
        if not firebase_db_url:
            logger.error("Firebase database URL not found in environment variables")
            return False

        # Initialize the app
        firebase_admin.initialize_app(cred, {"databaseURL": firebase_db_url})
        logger.info("Firebase initialized successfully")

        # Test Firebase connectivity immediately after initialization
        try:
            test_ref = db.reference("system_health")
            test_data = {"last_check": datetime.now(timezone.utc).isoformat(), "status": "healthy"}
            test_ref.set(test_data)
            logger.info("Firebase connectivity test successful")
        except Exception as test_error:
            logger.error(f"Firebase connectivity test failed: {test_error}")
            # Don't fail initialization, but log the issue

        # Reset attempt count on success
        _init_attempt_count = 0
        return True
    except Exception as e:
        logger.error(f"Error initializing Firebase: {e}")
        return False


def test_firebase_connectivity() -> bool:
    """Test Firebase connectivity and JWT token validity"""
    try:
        if not firebase_admin._apps:
            logger.warning("Firebase not initialized, attempting to initialize...")
            if not initialize_firebase():
                logger.error("Failed to initialize Firebase for connectivity test")
                return False

        # Test basic read operation
        test_ref = db.reference("system_health")
        current_time = datetime.now(timezone.utc).isoformat()

        # Test write operation
        test_data = {
            "last_connectivity_test": current_time,
            "status": "testing",
            "test_id": f"test_{int(time.time())}"
        }
        test_ref.set(test_data)

        # Test read operation
        read_result = test_ref.get()

        if read_result and read_result.get("last_connectivity_test") == current_time:
            logger.info("Firebase connectivity test PASSED - read/write operations successful")
            return True
        else:
            logger.error("Firebase connectivity test FAILED - read operation returned unexpected data")
            return False

    except Exception as e:
        error_str = str(e)
        if "invalid_grant" in error_str and "JWT" in error_str:
            logger.error(f"Firebase connectivity test FAILED - JWT token error: {error_str}")
        elif "404 Not Found" in error_str:
            # For new empty databases, 404 errors are expected - this means Firebase is accessible
            logger.info("Firebase connectivity test PASSED - 404 errors are expected for new empty databases")
            return True
        else:
            logger.error(f"Firebase connectivity test FAILED - general error: {error_str}")
        return False


# Helper function with retry logic
def _retry_operation(operation_func, *args, **kwargs):
    """Retry an operation with exponential backoff and JWT error handling"""
    max_retries = 3
    retry_delay = 1  # Start with 1 second delay

    for attempt in range(max_retries):
        try:
            return operation_func(*args, **kwargs)
        except Exception as e:
            error_str = str(e)

            # Handle JWT token errors specifically
            if "invalid_grant" in error_str and "JWT" in error_str:
                logger.warning(f"JWT token error detected on attempt {attempt + 1}: {error_str}")
                # For JWT errors, reinitialize Firebase and retry immediately
                if attempt < max_retries - 1:
                    logger.info("Attempting to reinitialize Firebase for JWT error...")
                    # Clear existing Firebase apps
                    try:
                        firebase_admin.delete_app(firebase_admin.get_app())
                        logger.info("Cleared existing Firebase app")
                    except:
                        logger.debug("No existing Firebase app to clear")

                    # Reinitialize
                    if initialize_firebase():
                        logger.info("Firebase reinitialized successfully, retrying operation...")
                        continue
                    else:
                        logger.error("Failed to reinitialize Firebase")

            if attempt == max_retries - 1:  # Last attempt
                # Re-raise the exception on the last attempt
                logger.error(f"Operation failed after {max_retries} attempts: {e}")
                raise

            # Add jitter to avoid thundering herd
            jitter = random.uniform(0, 0.5)
            sleep_time = retry_delay + jitter

            logger.warning(f"Operation failed, retrying in {sleep_time:.2f}s: {e}")
            time.sleep(sleep_time)

            # Exponential backoff
            retry_delay *= 2


# Generic data operations with retry and validation
def get_data(path: str) -> Optional[Dict[str, Any]]:
    """Get data from Firebase at the specified path with security validation"""
    # Validate path for security
    from src.utils.validation import validate_firebase_path, sanitize_input

    if not isinstance(path, str):
        logger.error(f"Invalid path type for get_data: {type(path)}")
        return None

    # Sanitize and validate path
    path = sanitize_input(path, max_length=200)
    if not validate_firebase_path(path):
        logger.error(f"Invalid or potentially dangerous Firebase path: {path}")
        return None

    # Check if Firebase is enabled
    from src.data_storage import USE_FIREBASE
    if not USE_FIREBASE:
        logger.debug("Firebase is disabled, returning None")
        return None

    try:
        if not initialize_firebase():
            return None

        def _get_operation():
            ref = db.reference(path)
            return ref.get()

        return _retry_operation(_get_operation)
    except Exception as e:
        logger.error(f"Error getting data from Firebase at {path}: {e}")
        return None


def set_data(path: str, data: Any) -> bool:
    """Set data in Firebase at the specified path with security validation"""
    # Validate path for security
    from src.utils.validation import validate_firebase_path, sanitize_input, is_safe_input

    if not isinstance(path, str):
        logger.error(f"Invalid path type for set_data: {type(path)}")
        return False

    # Sanitize and validate path
    path = sanitize_input(path, max_length=200)
    if not validate_firebase_path(path):
        logger.error(f"Invalid or potentially dangerous Firebase path: {path}")
        return False

    # Validate data if it's a string
    if isinstance(data, str):
        if not is_safe_input(data):
            logger.error(f"Potentially dangerous data detected for path {path}")
            return False
        data = sanitize_input(data, max_length=10000)
    elif isinstance(data, dict):
        # Recursively validate string values in dictionaries
        data = _sanitize_dict_data(data)

    # Check if Firebase is enabled
    from src.data_storage import USE_FIREBASE
    if not USE_FIREBASE:
        logger.debug("Firebase is disabled, skipping set operation")
        return True

    try:
        if not initialize_firebase():
            return False

        ref = db.reference(path)

        # Handle empty dictionaries by deleting the path (clearing the collection)
        if isinstance(data, dict) and not data:
            logger.info(f"Clearing collection at {path} (empty dictionary provided)")
            ref.delete()
            return True

        # Set the data normally
        ref.set(data)
        return True
    except Exception as e:
        logger.error(f"Error setting data in Firebase at {path}: {e}")
        return False


def _sanitize_dict_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """Recursively sanitize dictionary data"""
    from src.utils.validation import sanitize_input, is_safe_input

    if not isinstance(data, dict):
        return data

    sanitized = {}
    for key, value in data.items():
        # Sanitize keys
        if isinstance(key, str):
            if not is_safe_input(key):
                logger.warning(f"Skipping potentially dangerous key: {key}")
                continue
            key = sanitize_input(key, max_length=100)

        # Sanitize values
        if isinstance(value, str):
            if not is_safe_input(value):
                logger.warning(f"Sanitizing potentially dangerous value for key {key}")
            value = sanitize_input(value, max_length=10000)
        elif isinstance(value, dict):
            value = _sanitize_dict_data(value)
        elif isinstance(value, list):
            value = [_sanitize_dict_data(item) if isinstance(item, dict)
                    else sanitize_input(str(item), max_length=1000) if isinstance(item, str)
                    else item for item in value]

        sanitized[key] = value

    return sanitized


def update_data(path: str, data: Dict[str, Any]) -> bool:
    """Update data in Firebase at the specified path"""
    try:
        # Skip empty dictionaries - Firebase rejects these
        if not data:
            logger.debug(f"Skipping update for empty data at {path}")
            return True

        if not initialize_firebase():
            return False
        ref = db.reference(path)
        ref.update(data)
        return True
    except Exception as e:
        logger.error(f"Error updating data in Firebase at {path}: {e}")
        return False


def push_data(path: str, data: Any) -> Optional[str]:
    """Push data to Firebase list at the specified path, returns the generated key"""
    # Check if Firebase is enabled
    from src.data_storage import USE_FIREBASE
    if not USE_FIREBASE:
        logger.debug("Firebase is disabled, skipping push operation")
        return None

    try:
        if not initialize_firebase():
            return None
        ref = db.reference(path)
        new_ref = ref.push(data)
        return new_ref.key
    except Exception as e:
        logger.error(f"Error pushing data to Firebase at {path}: {e}")
        return None


def delete_data(path: str) -> bool:
    """Delete data from Firebase at the specified path"""
    # Check if Firebase is enabled
    from src.data_storage import USE_FIREBASE
    if not USE_FIREBASE:
        logger.debug("Firebase is disabled, skipping delete operation")
        return True

    try:
        if not initialize_firebase():
            return False
        ref = db.reference(path)
        ref.delete()
        return True
    except Exception as e:
        logger.error(f"Error deleting data from Firebase at {path}: {e}")
        return False


# Specific data operations for different entities


# User Points
def get_user_points(user_id: str = None) -> Dict[str, int]:
    """Get points for a specific user or all users"""
    if user_id:
        result = get_data(f"user_points/{user_id}")
        return {user_id: result} if result is not None else {}
    else:
        return get_data("user_points") or {}


def update_user_points(user_id: str, points: int) -> bool:
    """Update points for a specific user"""
    return set_data(f"user_points/{user_id}", points)


def update_user_points_batch(points_data: Dict[str, int]) -> bool:
    """Update points for multiple users"""
    return update_data("user_points", points_data)


# User Names
def get_user_names(user_id: str = None) -> Dict[str, str]:
    """Get names for a specific user or all users"""
    if user_id:
        result = get_data(f"user_names/{user_id}")
        return {user_id: result} if result is not None else {}
    else:
        return get_data("user_names") or {}


def update_user_name(user_id: str, name: str) -> bool:
    """Update name for a specific user"""
    return set_data(f"user_names/{user_id}", name)


def update_user_names_batch(names_data: Dict[str, str]) -> bool:
    """Update names for multiple users"""
    return update_data("user_names", names_data)


# User Phone Numbers
def get_user_phone_numbers(user_id: str = None) -> Dict[str, str]:
    """Get phone numbers for a specific user or all users"""
    if user_id:
        result = get_data(f"user_phone_numbers/{user_id}")
        return {user_id: result} if result is not None else {}
    else:
        return get_data("user_phone_numbers") or {}


def update_user_phone_number(user_id: str, phone_number: str) -> bool:
    """Update phone number for a specific user"""
    return set_data(f"user_phone_numbers/{user_id}", phone_number)


def update_user_phone_numbers_batch(phone_data: Dict[str, str]) -> bool:
    """Update phone numbers for multiple users"""
    return update_data("user_phone_numbers", phone_data)


# User Emails
def get_user_emails(user_id: str = None) -> Dict[str, str]:
    """Get emails for a specific user or all users"""
    if user_id:
        result = get_data(f"user_emails/{user_id}")
        return {user_id: result} if result is not None else {}
    else:
        return get_data("user_emails") or {}


def update_user_email(user_id: str, email: str) -> bool:
    """Update email for a specific user"""
    return set_data(f"user_emails/{user_id}", email)


def update_user_emails_batch(email_data: Dict[str, str]) -> bool:
    """Update emails for multiple users"""
    return update_data("user_emails", email_data)


# Order History
def get_user_order_history(user_id: str = None) -> Dict[str, List[Dict[str, Any]]]:
    """Get order history for a specific user or all users"""
    if user_id:
        result = get_data(f"user_order_history/{user_id}")
        return {user_id: result} if result is not None else {}
    else:
        return get_data("user_order_history") or {}


def add_order_to_history(user_id: str, order: Dict[str, Any]) -> bool:
    """Add an order to a user's history"""
    # Get current history
    history = get_data(f"user_order_history/{user_id}") or []
    # Add new order
    history.append(order)
    # Update history
    return set_data(f"user_order_history/{user_id}", history)


def update_user_order_history_batch(
    history_data: Dict[str, List[Dict[str, Any]]],
) -> bool:
    """Update order history for multiple users"""
    return update_data("user_order_history", history_data)


# Favorite Orders
def get_favorite_orders(user_id: str = None) -> Dict[str, List[Dict[str, Any]]]:
    """Get favorite orders for a specific user or all users"""
    try:
        # If user_id is None and called from data_storage.load_user_data, return empty dict to avoid errors
        if user_id is None and any(
            frame.function == "load_user_data" for frame in inspect.stack()
        ):
            logger.debug(
                "get_favorite_orders called without user_id from load_user_data, returning empty dict"
            )
            return {}

        if user_id:
            result = get_data(f"favorite_orders/{user_id}")
            return {user_id: result} if result is not None else {}
        else:
            return get_data("favorite_orders") or {}
    except Exception as e:
        logger.error(f"Error getting favorite orders: {e}")
        return {}


def update_favorite_orders(user_id: str, favorites: List[Dict[str, Any]]) -> bool:
    """Update favorite orders for a specific user"""
    return set_data(f"favorite_orders/{user_id}", favorites)


def add_favorite_order(user_id: str, order: Dict[str, Any]) -> bool:
    """Add a favorite order for a specific user"""
    # Get current favorites
    favorites = get_data(f"favorite_orders/{user_id}") or []
    # Add new favorite
    favorites.append(order)
    # Update favorites
    return set_data(f"favorite_orders/{user_id}", favorites)


def delete_favorite_order(user_id: str, index: int) -> bool:
    """Delete a favorite order for a specific user by index"""
    # Get current favorites
    favorites = get_data(f"favorite_orders/{user_id}") or []
    # Check if index is valid
    if 0 <= index < len(favorites):
        # Remove favorite at index
        del favorites[index]
        # Update favorites
        return set_data(f"favorite_orders/{user_id}", favorites)
    return False


def update_favorite_orders_batch(
    favorites_data: Dict[str, List[Dict[str, Any]]],
) -> bool:
    """Update favorite orders for multiple users"""
    return update_data("favorite_orders", favorites_data)


# Current Orders
def get_current_orders(user_id: str = None) -> Dict[str, Dict[str, Any]]:
    """Get current orders for a specific user or all users"""
    if user_id:
        result = get_data(f"current_orders/{user_id}")
        return {user_id: result} if result is not None else {}
    else:
        return get_data("current_orders") or {}


def update_current_order(user_id: str, order: Dict[str, Any]) -> bool:
    """Update current order for a specific user"""
    return set_data(f"current_orders/{user_id}", order)


def delete_current_order(user_id: str) -> bool:
    """Delete current order for a specific user"""
    return delete_data(f"current_orders/{user_id}")


def update_current_orders_batch(orders_data: Dict[str, Dict[str, Any]]) -> bool:
    """Update current orders for multiple users"""
    return update_data("current_orders", orders_data)


# Order Status
def get_order_status(user_id: str = None) -> Dict[str, str]:
    """Get order status for a specific user or all users"""
    if user_id:
        result = get_data(f"order_status/{user_id}")
        return {user_id: result} if result is not None else {}
    else:
        return get_data("order_status") or {}


def update_order_status(user_id: str, status: str) -> bool:
    """Update order status for a specific user"""
    return set_data(f"order_status/{user_id}", status)


def update_order_status_batch(status_data: Dict[str, str]) -> bool:
    """Update order status for multiple users"""
    return update_data("order_status", status_data)


# Pending Admin Reviews
def get_pending_admin_reviews(order_number: str = None) -> Dict[str, Dict[str, Any]]:
    """Get pending admin reviews for a specific order or all orders"""
    if order_number:
        result = get_data(f"pending_admin_reviews/{order_number}")
        return {order_number: result} if result is not None else {}
    else:
        return get_data("pending_admin_reviews") or {}


def add_pending_admin_review(order_number: str, order_data: Dict[str, Any]) -> bool:
    """Add an order for admin review"""
    return set_data(f"pending_admin_reviews/{order_number}", order_data)


def delete_pending_admin_review(order_number: str) -> bool:
    """Delete a specific pending admin review from Firebase"""
    try:
        if not order_number:
            logger.error("Cannot delete pending admin review: order_number is empty")
            return False

        # Delete the specific order under pending_admin_reviews
        ref = db.reference(f"pending_admin_reviews/{order_number}")

        # First check if it exists
        data = ref.get()
        if data is None:
            logger.warning(
                f"Order #{order_number} not found in Firebase pending_admin_reviews"
            )
            return True  # If it's already gone, we consider this a success

        # Delete the order
        ref.delete()

        # Verify deletion
        verification = db.reference(f"pending_admin_reviews/{order_number}").get()
        if verification is None:
            logger.info(
                f"Successfully deleted order #{order_number} from pending_admin_reviews in Firebase"
            )
            return True
        else:
            logger.warning(
                f"Failed to delete order #{order_number} from Firebase: Still exists after deletion"
            )
            return False
    except Exception as e:
        logger.error(f"Error deleting order #{order_number} from Firebase: {str(e)}")
        return False


def update_pending_admin_reviews_batch(reviews_data: Dict[str, Dict[str, Any]]) -> bool:
    """Update pending admin reviews for multiple orders"""
    return update_data("pending_admin_reviews", reviews_data)


# Admin Remarks
def get_admin_remarks(order_number: str = None) -> Dict[str, str]:
    """Get admin remarks for a specific order or all orders"""
    if order_number:
        result = get_data(f"admin_remarks/{order_number}")
        return {order_number: result} if result is not None else {}
    else:
        return get_data("admin_remarks") or {}


def update_admin_remark(order_number: str, remark: str) -> bool:
    """Update admin remark for a specific order"""
    return set_data(f"admin_remarks/{order_number}", remark)


def update_admin_remarks_batch(remarks_data: Dict[str, str]) -> bool:
    """Update admin remarks for multiple orders"""
    return update_data("admin_remarks", remarks_data)


def delete_admin_remark(order_number: str) -> bool:
    """Delete a specific admin remark from Firebase"""
    try:
        if not order_number:
            logger.error("Cannot delete admin remark: order_number is empty")
            return False

        # Delete the specific remark under admin_remarks
        ref = db.reference(f"admin_remarks/{order_number}")

        # First check if it exists
        data = ref.get()
        if data is None:
            logger.warning(f"Order #{order_number} not found in Firebase admin_remarks")
            return True  # If it's already gone, we consider this a success

        # Delete the remark
        ref.delete()

        # Verify deletion
        verification = db.reference(f"admin_remarks/{order_number}").get()
        if verification is None:
            logger.info(
                f"Successfully deleted remark for order #{order_number} from admin_remarks in Firebase"
            )
            return True
        else:
            logger.warning(
                f"Failed to delete remark for order #{order_number} from Firebase: Still exists after deletion"
            )
            return False
    except Exception as e:
        logger.error(
            f"Error deleting remark for order #{order_number} from Firebase: {str(e)}"
        )
        return False


# Awaiting Receipt
def get_awaiting_receipt(order_number: str = None) -> Dict[str, Dict[str, Any]]:
    """Get orders awaiting receipt for a specific order or all orders"""
    if order_number:
        result = get_data(f"awaiting_receipt/{order_number}")
        return {order_number: result} if result is not None else {}
    else:
        return get_data("awaiting_receipt") or {}


def add_awaiting_receipt(order_number: str, order_data: Dict[str, Any]) -> bool:
    """Add an order to awaiting receipt"""
    return set_data(f"awaiting_receipt/{order_number}", order_data)


def delete_awaiting_receipt(order_number: str) -> bool:
    """Delete an order from awaiting receipt"""
    return delete_data(f"awaiting_receipt/{order_number}")


def update_awaiting_receipt_batch(receipt_data: Dict[str, Dict[str, Any]]) -> bool:
    """Update awaiting receipt for multiple orders"""
    return update_data("awaiting_receipt", receipt_data)


# Migration utilities
def migrate_data_to_firebase(data: Dict[str, Any], path: str) -> bool:
    """Migrate data from JSON file to Firebase"""
    return set_data(path, data)


def clear_pending_admin_reviews() -> bool:
    """Clear all pending admin reviews from Firebase"""
    try:
        # Delete the entire pending_admin_reviews node
        db.reference("pending_admin_reviews").delete()
        logger.info("Successfully cleared all pending admin reviews from Firebase")
        return True
    except Exception as e:
        logger.error(f"Error clearing pending admin reviews from Firebase: {str(e)}")
        return False


# Atomic transaction operations for race condition prevention
def atomic_order_assignment(order_number: str, personnel_id: str, delivery_fee: float) -> Optional[str]:
    """
    Atomically assign an order to delivery personnel using Firebase transactions.
    This prevents race conditions when multiple personnel try to accept the same order.

    Args:
        order_number: The order number to assign
        personnel_id: ID of the delivery personnel
        delivery_fee: Delivery fee for the order

    Returns:
        assignment_id if successful, None if order already assigned or error occurred
    """
    try:
        if not initialize_firebase():
            logger.error("Firebase not initialized for atomic order assignment")
            return None

        import uuid
        assignment_id = f"assign_{uuid.uuid4().hex[:8]}"

        def transaction_update(current_data):
            """Transaction function to atomically check and assign order"""
            if current_data is None:
                # Order doesn't exist
                logger.warning(f"Order {order_number} not found during atomic assignment")
                return None

            # Check if order is still available for assignment
            if current_data.get('delivery_status') != 'pending_assignment':
                logger.info(f"Order {order_number} already assigned (status: {current_data.get('delivery_status')})")
                return None

            # Check if order is already assigned to someone
            if current_data.get('assigned_to'):
                logger.info(f"Order {order_number} already assigned to {current_data.get('assigned_to')}")
                return None

            # Update order with assignment details (ensure no None values)
            current_data['delivery_status'] = 'assigned'
            current_data['assigned_to'] = personnel_id or ''
            current_data['assigned_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            current_data['assignment_id'] = assignment_id or ''
            current_data['delivery_fee'] = delivery_fee if delivery_fee is not None else 0.0

            # Ensure all required fields exist and are not None
            if not current_data.get('order_number'):
                current_data['order_number'] = order_number
            if not current_data.get('restaurant'):
                current_data['restaurant'] = current_data.get('restaurant', 'Unknown Restaurant')
            if not current_data.get('items'):
                current_data['items'] = current_data.get('items', [])
            if current_data.get('subtotal') is None:
                current_data['subtotal'] = current_data.get('subtotal', 0)
            if current_data.get('total') is None:
                current_data['total'] = current_data.get('total', 0)

            return current_data

        # Execute the transaction
        order_ref = db.reference(f"confirmed_orders/{order_number}")
        result = order_ref.transaction(transaction_update)

        if result is None:
            logger.warning(f"Atomic assignment failed for order {order_number} - order not available")
            return None

        logger.info(f"Successfully assigned order {order_number} to personnel {personnel_id} atomically")
        return assignment_id

    except Exception as e:
        logger.error(f"Error in atomic order assignment for {order_number}: {e}")
        return None


def atomic_capacity_check_and_increment(personnel_id: str, max_capacity: int = 5) -> bool:
    """
    Atomically check capacity and increment if under limit using Firebase transactions.
    This prevents race conditions in capacity tracking.

    Args:
        personnel_id: ID of the delivery personnel
        max_capacity: Maximum allowed capacity (default 5)

    Returns:
        True if capacity was successfully incremented, False if at capacity or error
    """
    try:
        if not initialize_firebase():
            logger.error("Firebase not initialized for atomic capacity check")
            return False

        def transaction_update(current_data):
            """Transaction function to atomically check and increment capacity"""
            if current_data is None:
                current_data = {'current_orders': 0, 'active_order_numbers': []}

            current_capacity = current_data.get('current_orders', 0)

            # Check if at capacity
            if current_capacity >= max_capacity:
                logger.info(f"Personnel {personnel_id} at capacity ({current_capacity}/{max_capacity})")
                return None  # Return None to abort transaction

            # Increment capacity
            current_data['current_orders'] = current_capacity + 1
            current_data['last_updated'] = datetime.now().isoformat()

            return current_data

        # Execute the transaction
        capacity_ref = db.reference(f"delivery_personnel_capacity_tracking/{personnel_id}")
        result = capacity_ref.transaction(transaction_update)

        if result is None:
            logger.info(f"Capacity check failed for personnel {personnel_id} - at maximum capacity")
            return False

        logger.info(f"Successfully incremented capacity for personnel {personnel_id}")
        return True

    except Exception as e:
        logger.error(f"Error in atomic capacity check for {personnel_id}: {e}")
        return False


def atomic_capacity_decrement(personnel_id: str, order_number: str) -> bool:
    """
    Atomically decrement capacity when an order is completed or cancelled.

    Args:
        personnel_id: ID of the delivery personnel
        order_number: Order number being completed/cancelled

    Returns:
        True if capacity was successfully decremented, False otherwise
    """
    try:
        if not initialize_firebase():
            logger.error("Firebase not initialized for atomic capacity decrement")
            return False

        def transaction_update(current_data):
            """Transaction function to atomically decrement capacity"""
            if current_data is None:
                logger.warning(f"No capacity data found for personnel {personnel_id}")
                return None

            current_capacity = current_data.get('current_orders', 0)
            active_orders = current_data.get('active_order_numbers', [])

            # Remove order from active list if present
            if order_number in active_orders:
                active_orders.remove(order_number)

            # Decrement capacity (ensure it doesn't go below 0)
            new_capacity = max(0, current_capacity - 1)

            current_data['current_orders'] = new_capacity
            current_data['active_order_numbers'] = active_orders
            current_data['last_updated'] = datetime.now().isoformat()

            return current_data

        # Execute the transaction
        capacity_ref = db.reference(f"delivery_personnel_capacity_tracking/{personnel_id}")
        result = capacity_ref.transaction(transaction_update)

        if result is None:
            logger.warning(f"Capacity decrement failed for personnel {personnel_id}")
            return False

        logger.info(f"Successfully decremented capacity for personnel {personnel_id}")
        return True

    except Exception as e:
        logger.error(f"Error in atomic capacity decrement for {personnel_id}: {e}")
        return False


def initialize_sample_data() -> bool:
    """Initialize Firebase database with sample data for presentation/demo purposes"""
    try:
        if not initialize_firebase():
            logger.error("Failed to initialize Firebase for sample data creation")
            return False

        logger.info("Starting Firebase database initialization with sample data...")

        # First, initialize the root with a simple structure to establish the database
        try:
            root_ref = db.reference("/")
            root_ref.update({"initialized": True, "timestamp": datetime.now(timezone.utc).isoformat()})
            logger.info("✓ Successfully initialized Firebase root")
        except Exception as e:
            logger.warning(f"Could not initialize root, continuing anyway: {e}")

        # Initialize all data in one comprehensive structure
        complete_database_structure = {
            "areas": {
                "areas": [
                    {"id": 1, "name": "Sample Area 1"},
                    {"id": 2, "name": "Sample Area 2"},
                    {"id": 3, "name": "Sample Area 3"}
                ]
            },
            "restaurants": {
                "restaurants": [
                    {"id": 1, "name": "Test Restaurant 1", "area_id": 1},
                    {"id": 2, "name": "Test Restaurant 2", "area_id": 1},
                    {"id": 3, "name": "Test Restaurant 3", "area_id": 2},
                    {"id": 4, "name": "Test Restaurant 4", "area_id": 2},
                    {"id": 5, "name": "Test Restaurant 5", "area_id": 3},
                    {"id": 6, "name": "Test Restaurant 6", "area_id": 3}
                ]
            },
            "menus": {
                "default_menu_items": [
                    {"id": 1, "name": "Sample Menu Item 1", "price": 150, "description": "Sample dish 1"},
                    {"id": 2, "name": "Sample Menu Item 2", "price": 180, "description": "Sample dish 2"},
                    {"id": 3, "name": "Sample Menu Item 3", "price": 200, "description": "Sample dish 3"}
                ],
                "restaurant_menus": {
                    "restaurant_1": [
                        {"id": 1, "name": "Sample Menu Item 1", "price": 150, "description": "Delicious sample dish 1"},
                        {"id": 2, "name": "Sample Menu Item 2", "price": 200, "description": "Tasty sample dish 2"},
                        {"id": 3, "name": "Sample Menu Item 3", "price": 180, "description": "Popular sample dish 3"}
                    ],
                    "restaurant_2": [
                        {"id": 4, "name": "Sample Menu Item 4", "price": 220, "description": "Special sample dish 4"},
                        {"id": 5, "name": "Sample Menu Item 5", "price": 160, "description": "Traditional sample dish 5"},
                        {"id": 6, "name": "Sample Menu Item 6", "price": 190, "description": "Modern sample dish 6"}
                    ],
                    "restaurant_3": [
                        {"id": 7, "name": "Sample Menu Item 7", "price": 170, "description": "Classic sample dish 7"},
                        {"id": 8, "name": "Sample Menu Item 8", "price": 210, "description": "Premium sample dish 8"}
                    ],
                    "restaurant_4": [
                        {"id": 9, "name": "Sample Menu Item 9", "price": 140, "description": "Budget sample dish 9"},
                        {"id": 10, "name": "Sample Menu Item 10", "price": 250, "description": "Luxury sample dish 10"}
                    ],
                    "restaurant_5": [
                        {"id": 11, "name": "Sample Menu Item 11", "price": 195, "description": "Signature sample dish 11"},
                        {"id": 12, "name": "Sample Menu Item 12", "price": 175, "description": "House special sample dish 12"}
                    ],
                    "restaurant_6": [
                        {"id": 13, "name": "Sample Menu Item 13", "price": 165, "description": "Chef's choice sample dish 13"},
                        {"id": 14, "name": "Sample Menu Item 14", "price": 230, "description": "Gourmet sample dish 14"}
                    ]
                }
            },
            "delivery_locations": {
                "delivery_locations": [
                    {"id": 1, "name": "Test Location 1", "description": "Sample delivery location 1"},
                    {"id": 2, "name": "Test Location 2", "description": "Sample delivery location 2"},
                    {"id": 3, "name": "Test Location 3", "description": "Sample delivery location 3"},
                    {"id": 4, "name": "Test Location 4", "description": "Sample delivery location 4"},
                    {"id": 5, "name": "Test Location 5", "description": "Sample delivery location 5"}
                ]
            },
            "delivery_fees": {
                "delivery_fees": [
                    # Sample Area 1 to all locations
                    {"area_id": 1, "location_id": 1, "fee": 25},
                    {"area_id": 1, "location_id": 2, "fee": 30},
                    {"area_id": 1, "location_id": 3, "fee": 35},
                    {"area_id": 1, "location_id": 4, "fee": 40},
                    {"area_id": 1, "location_id": 5, "fee": 45},
                    # Sample Area 2 to all locations
                    {"area_id": 2, "location_id": 1, "fee": 30},
                    {"area_id": 2, "location_id": 2, "fee": 25},
                    {"area_id": 2, "location_id": 3, "fee": 30},
                    {"area_id": 2, "location_id": 4, "fee": 35},
                    {"area_id": 2, "location_id": 5, "fee": 40},
                    # Sample Area 3 to all locations
                    {"area_id": 3, "location_id": 1, "fee": 40},
                    {"area_id": 3, "location_id": 2, "fee": 35},
                    {"area_id": 3, "location_id": 3, "fee": 25},
                    {"area_id": 3, "location_id": 4, "fee": 30},
                    {"area_id": 3, "location_id": 5, "fee": 35}
                ]
            },
            # Empty user data collections to prevent 404 errors
            "user_order_history": {},
            "user_points": {},
            "user_names": {},
            "user_phone_numbers": {},
            "user_emails": {},
            "favorite_orders": {},
            "current_orders": {},
            "order_status": {},
            "pending_admin_reviews": {},
            "admin_remarks": {},
            "awaiting_receipt": {},
            "delivery_locations_temp": {},
            "user_order_counts": {},
            "current_order_numbers": {},
            "delivery_personnel": {},
            "delivery_personnel_assignments": {},
            "delivery_personnel_availability": {},
            "delivery_personnel_capacity": {},
            "delivery_personnel_zones": {},
            "delivery_personnel_performance": {},
            "delivery_personnel_earnings": {}
        }

        # Set the entire database structure at once
        try:
            root_ref = db.reference("/")
            root_ref.update(complete_database_structure)
            logger.info("🎉 Firebase database initialization completed successfully!")
            logger.info("Sample data includes:")
            logger.info("- 3 Sample Areas")
            logger.info("- 6 Test Restaurants")
            logger.info("- 14 Sample Menu Items")
            logger.info("- 5 Test Delivery Locations")
            logger.info("- 15 Sample Delivery Fees")
            logger.info("- All required empty user data collections")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize Firebase database structure: {e}")
            return False

    except Exception as e:
        logger.error(f"Error during Firebase database initialization: {e}")
        return False


def clear_admin_remarks() -> bool:
    """Clear all admin remarks from Firebase"""
    try:
        # Delete the entire admin_remarks node
        db.reference("admin_remarks").delete()
        logger.info("Successfully cleared all admin remarks from Firebase")
        return True
    except Exception as e:
        logger.error(f"Error clearing admin remarks from Firebase: {str(e)}")
        return False
